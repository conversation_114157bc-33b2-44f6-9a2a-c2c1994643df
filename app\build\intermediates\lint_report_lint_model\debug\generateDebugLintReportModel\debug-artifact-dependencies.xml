<dependencies>
  <compile
      roots="androidx.camera:camera-video:1.3.1@aar,androidx.camera:camera-extensions:1.3.1@aar,androidx.camera:camera-view:1.3.1@aar,androidx.camera:camera-lifecycle:1.3.1@aar,androidx.camera:camera-camera2:1.3.1@aar,androidx.camera:camera-core:1.3.1@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-tooling-data-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.compose.ui:ui-tooling-android:1.7.0@aar,androidx.compose.ui:ui-test-manifest:1.7.0@aar,com.google.mlkit:face-detection:16.1.6@aar,com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar,com.google.mlkit:vision-common:17.2.0@aar,com.google.mlkit:common:18.10.0@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.mlkit:vision-interfaces:16.1.0@aar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.core:core-ktx:1.13.1@aar,androidx.compose.runtime:runtime-saveable-android:1.7.0@aar,androidx.compose.runtime:runtime-android:1.7.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,com.google.firebase:firebase-components:16.1.0@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:16.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.collection:collection-jvm:1.4.0@jar,androidx.exifinterface:exifinterface:1.3.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,javax.inject:javax.inject:1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.firebase:firebase-annotations:16.0.0@jar">
    <dependency
        name="androidx.camera:camera-video:1.3.1@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-extensions:1.3.1@aar"
        simpleName="androidx.camera:camera-extensions"/>
    <dependency
        name="androidx.camera:camera-view:1.3.1@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.3.1@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.3.1@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.3.1@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="com.google.mlkit:face-detection:16.1.6@aar"
        simpleName="com.google.mlkit:face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-face-detection"/>
    <dependency
        name="com.google.mlkit:vision-common:17.2.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.10.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.1.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:16.1.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:16.1.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.2@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.0.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
  </compile>
  <package
      roots="androidx.camera:camera-video:1.3.1@aar,androidx.camera:camera-extensions:1.3.1@aar,androidx.camera:camera-view:1.3.1@aar,androidx.camera:camera-lifecycle:1.3.1@aar,androidx.camera:camera-camera2:1.3.1@aar,androidx.camera:camera-core:1.3.1@aar,com.google.mlkit:face-detection:16.1.6@aar,com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar,com.google.mlkit:vision-common:17.2.0@aar,com.google.mlkit:common:18.10.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.compose.material3:material3-android:1.3.0@aar,androidx.compose.material:material-android:1.7.0@aar,androidx.compose.material:material-ripple-android:1.7.0@aar,androidx.compose.foundation:foundation-android:1.7.0@aar,androidx.compose.animation:animation-android:1.7.0@aar,androidx.compose.foundation:foundation-layout-android:1.7.0@aar,androidx.compose.animation:animation-core-android:1.7.0@aar,androidx.compose.ui:ui-tooling-data-android:1.7.0@aar,androidx.compose.ui:ui-unit-android:1.7.0@aar,androidx.compose.ui:ui-geometry-android:1.7.0@aar,androidx.compose.ui:ui-util-android:1.7.0@aar,androidx.compose.ui:ui-text-android:1.7.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar,androidx.compose.ui:ui-tooling-android:1.7.0@aar,androidx.compose.ui:ui-graphics-android:1.7.0@aar,androidx.compose.ui:ui-test-manifest:1.7.0@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.mlkit:vision-interfaces:16.1.0@aar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.3.6@aar,androidx.fragment:fragment:1.3.6@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.compose.material:material-icons-core-android:1.7.0@aar,androidx.compose.ui:ui-android:1.7.0@aar,androidx.loader:loader:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.customview:customview:1.0.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-process:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-common-java8:2.8.3@jar,androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.compose.runtime:runtime-saveable-android:1.7.0@aar,androidx.compose.runtime:runtime-android:1.7.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,androidx.collection:collection-ktx:1.4.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-jvm:1.4.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,com.google.firebase:firebase-components:16.1.0@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:16.1.0@jar,androidx.exifinterface:exifinterface:1.3.2@aar,androidx.exifinterface:exifinterface:1.3.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.guava:listenablefuture:1.0@jar,javax.inject:javax.inject:1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.firebase:firebase-annotations:16.0.0@jar">
    <dependency
        name="androidx.camera:camera-video:1.3.1@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-extensions:1.3.1@aar"
        simpleName="androidx.camera:camera-extensions"/>
    <dependency
        name="androidx.camera:camera-view:1.3.1@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.3.1@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.3.1@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.3.1@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="com.google.mlkit:face-detection:16.1.6@aar"
        simpleName="com.google.mlkit:face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-face-detection"/>
    <dependency
        name="com.google.mlkit:vision-common:17.2.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.10.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.3.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.7.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.1.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.3.6@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.7.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.7.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:16.1.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:16.1.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.2@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.0.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
  </package>
</dependencies>
