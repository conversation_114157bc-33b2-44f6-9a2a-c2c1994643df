package com.wendy.face.detection

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions

/**
 * 人脸检测管理器
 * 负责配置和执行人脸检测功能
 */
class FaceDetectorManager {
    
    companion object {
        private const val TAG = "FaceDetectorManager"
    }
    
    private val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        .setMinFaceSize(0.05f)  // 降低最小人脸大小阈值
        .enableTracking()
        .build()
    
    private val faceDetector = FaceDetection.getClient(faceDetectorOptions)
    
    /**
     * 检测图片中的人脸
     * @param bitmap 要检测的图片
     * @param onSuccess 检测成功回调
     * @param onFailure 检测失败回调
     */
    fun detectFaces(
        bitmap: Bitmap,
        onSuccess: (List<Face>, Int, Int) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        try {
            // 创建InputImage从bitmap进行人脸检测
            val inputImage = InputImage.fromBitmap(bitmap, 0)
            
            // 执行人脸检测
            faceDetector.process(inputImage)
                .addOnSuccessListener { detectedFaces ->
                    Log.d(TAG, "Face detection completed, faces found: ${detectedFaces.size}")
                    Log.d(TAG, "Image dimensions: ${bitmap.width} x ${bitmap.height}")
                    detectedFaces.forEachIndexed { index, face ->
                        Log.d(TAG, "Face $index: boundingBox=${face.boundingBox}, landmarks=${face.allLandmarks.size}")
                    }
                    onSuccess(detectedFaces, bitmap.width, bitmap.height)
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Face detection failed", e)
                    onFailure(e)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating InputImage", e)
            onFailure(e)
        }
    }
    
    /**
     * 执行测试人脸检测
     * @param testBitmap 测试图片
     * @param onResult 结果回调
     */
    fun testFaceDetection(
        testBitmap: Bitmap,
        onResult: (List<Face>, Int, Int, Bitmap) -> Unit
    ) {
        Log.d(TAG, "Starting test face detection")
        
        // 创建一个假的人脸检测结果用于测试坐标转换
        val testFaces = emptyList<Face>() // 暂时使用空列表，主要测试图片显示和坐标系统
        onResult(testFaces, testBitmap.width, testBitmap.height, testBitmap)
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            faceDetector.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error closing face detector", e)
        }
    }
}
