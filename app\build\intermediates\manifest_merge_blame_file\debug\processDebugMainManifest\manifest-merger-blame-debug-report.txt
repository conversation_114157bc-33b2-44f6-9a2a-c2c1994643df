1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wendy.face"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera.any" />
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:5-64
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:19-61
12
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission
14-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:5-8:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:8:9-35
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:5-80
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:22-77
18
19    <queries>
19-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
20        <intent>
20-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
21            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30    <!-- <uses-sdk android:minSdkVersion="14"/> -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
32    <uses-permission android:name="android.permission.INTERNET" />
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
33
34    <application
34-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:11:5-38:19
35        android:allowBackup="true"
35-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:12:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:13:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:14:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:15:9-43
42        android:label="@string/app_name"
42-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:16:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:17:9-54
44        android:supportsRtl="true"
44-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:18:9-35
45        android:theme="@style/Theme.Face" >
45-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:19:9-42
46        <activity
46-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:21:9-31:20
47            android:name="com.wendy.face.MainActivity"
47-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:22:13-41
48            android:exported="true"
48-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:23:13-36
49            android:label="@string/app_name"
49-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24:13-45
50            android:theme="@style/Theme.Face" >
50-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:25:13-46
51            <intent-filter>
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:26:13-30:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:17-69
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:17-77
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:32:9-37:20
58            android:name="com.wendy.face.TestCameraActivity"
58-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:33:13-47
59            android:exported="false"
59-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:34:13-37
60            android:label="Test Camera"
60-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:35:13-40
61            android:theme="@style/Theme.Face" >
61-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:36:13-46
62        </activity>
63
64        <uses-library
64-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
65            android:name="androidx.camera.extensions.impl"
65-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
66            android:required="false" />
66-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
67
68        <service
68-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
69            android:name="androidx.camera.core.impl.MetadataHolderService"
69-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
70            android:enabled="false"
70-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
71            android:exported="false" >
71-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
72            <meta-data
72-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
73                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
73-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
74                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
75        </service>
76        <service
76-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
77            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
77-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
78            android:directBootAware="true"
78-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:17:13-43
79            android:exported="false" >
79-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
80            <meta-data
80-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
81                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
81-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
82                android:value="com.google.firebase.components.ComponentRegistrar" />
82-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
83            <meta-data
83-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
84                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
84-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
85                android:value="com.google.firebase.components.ComponentRegistrar" />
85-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
86            <meta-data
86-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:20:13-22:85
87                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
87-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:21:17-120
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:22:17-82
89        </service>
90
91        <provider
91-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:9:9-13:38
92            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
92-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:10:13-78
93            android:authorities="com.wendy.face.mlkitinitprovider"
93-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:11:13-69
94            android:exported="false"
94-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:12:13-37
95            android:initOrder="99" />
95-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:13:13-35
96
97        <activity
97-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
98            android:name="androidx.compose.ui.tooling.PreviewActivity"
98-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
99            android:exported="true" />
99-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
100        <activity
100-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
101            android:name="androidx.activity.ComponentActivity"
101-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
102            android:exported="true" />
102-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
103        <activity
103-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
104            android:name="com.google.android.gms.common.api.GoogleApiActivity"
104-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
105            android:exported="false"
105-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
106            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
106-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
107
108        <meta-data
108-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
109            android:name="com.google.android.gms.version"
109-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
110            android:value="@integer/google_play_services_version" />
110-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
111
112        <provider
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
113            android:name="androidx.startup.InitializationProvider"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
114            android:authorities="com.wendy.face.androidx-startup"
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
115            android:exported="false" >
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
116            <meta-data
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.emoji2.text.EmojiCompatInitializer"
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
118                android:value="androidx.startup" />
118-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
119            <meta-data
119-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
120-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
121                android:value="androidx.startup" />
121-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
122            <meta-data
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
124                android:value="androidx.startup" />
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
125        </provider>
126
127        <service
127-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
128            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
128-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
129            android:exported="false" >
129-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
130            <meta-data
130-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
131                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
131-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
132                android:value="cct" />
132-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
133        </service>
134        <service
134-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
135            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
135-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
136            android:exported="false"
136-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
137            android:permission="android.permission.BIND_JOB_SERVICE" >
137-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
138        </service>
139
140        <receiver
140-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
141            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
141-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
142            android:exported="false" />
142-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
143        <receiver
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
144            android:name="androidx.profileinstaller.ProfileInstallReceiver"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
145            android:directBootAware="false"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
146            android:enabled="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
147            android:exported="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
148            android:permission="android.permission.DUMP" >
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
150                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
153                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
156                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
159                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
160            </intent-filter>
161        </receiver>
162    </application>
163
164</manifest>
