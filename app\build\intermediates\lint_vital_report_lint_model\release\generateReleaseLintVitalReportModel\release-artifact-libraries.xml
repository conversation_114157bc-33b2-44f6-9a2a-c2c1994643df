<libraries>
  <library
      name="androidx.camera:camera-video:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a72b9a16bf9098dedc125ca5a5997ce\transformed\camera-video-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-video:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a72b9a16bf9098dedc125ca5a5997ce\transformed\camera-video-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-extensions:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-extensions:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-view:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5ee2f8e7d76ed41d62768c46df44c9a\transformed\camera-view-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-view:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5ee2f8e7d76ed41d62768c46df44c9a\transformed\camera-view-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-lifecycle:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5641801b9321f8206579588aeb3938d4\transformed\camera-lifecycle-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-lifecycle:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5641801b9321f8206579588aeb3938d4\transformed\camera-lifecycle-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-camera2:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-camera2:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-core:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4474d489ca6281413221951f43cffafe\transformed\camera-core-1.3.1\jars\classes.jar"
      resolved="androidx.camera:camera-core:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4474d489ca6281413221951f43cffafe\transformed\camera-core-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:face-detection:16.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e6da217ce3e7416c966795a8fd1316\transformed\face-detection-16.1.6\jars\classes.jar"
      resolved="com.google.mlkit:face-detection:16.1.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e6da217ce3e7416c966795a8fd1316\transformed\face-detection-16.1.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-face-detection:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-common:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-common:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:common:18.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\jars\classes.jar"
      resolved="com.google.mlkit:common:18.10.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-interfaces:16.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5da5eaa7210a0f0b1093434bb7fa15a2\transformed\vision-interfaces-16.1.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-interfaces:16.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5da5eaa7210a0f0b1093434bb7fa15a2\transformed\vision-interfaces-16.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74d6a56438223a66f80fad6d2a2fe7bf\transformed\play-services-tasks-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74d6a56438223a66f80fad6d2a2fe7bf\transformed\play-services-tasks-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\378a970638a61424dd52cc174fd66606\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\378a970638a61424dd52cc174fd66606\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27918c06b18d0382bb81e4832935fb7c\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27918c06b18d0382bb81e4832935fb7c\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d07ad02806221aff681e526df751834b\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d07ad02806221aff681e526df751834b\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18bc42f51ac803824f11dfcd211bfb51\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18bc42f51ac803824f11dfcd211bfb51\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c461d709d0ca97a6b7a6b2ad45cd2ae\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c461d709d0ca97a6b7a6b2ad45cd2ae\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12546c0b1520cc851649e7f5c69150e2\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12546c0b1520cc851649e7f5c69150e2\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d7f3461144cf50799f37105b86ea65\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6d7f3461144cf50799f37105b86ea65\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50f8bd46ebe8d4f1e383c76308b17c26\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50f8bd46ebe8d4f1e383c76308b17c26\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4723f63d599634bdb83709f096c017fd\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4723f63d599634bdb83709f096c017fd\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c56129fe800ddf3800701b25673db6af\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c56129fe800ddf3800701b25673db6af\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a8f5bbf0be5cef9228bdc8aaca706b9\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a8f5bbf0be5cef9228bdc8aaca706b9\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab857242c4b95d10e3ce9a2ce1208eb\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab857242c4b95d10e3ce9a2ce1208eb\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05ef868ca0f438ebd98b29812d4e3a6\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05ef868ca0f438ebd98b29812d4e3a6\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84fa34e792243668b2d55eb447ca8ea2\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84fa34e792243668b2d55eb447ca8ea2\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a8545ce1087bfdc541dcdc6cfe3c970\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a8545ce1087bfdc541dcdc6cfe3c970\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70aded82f84e94baee0fb0dbee5c0ebe\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70aded82f84e94baee0fb0dbee5c0ebe\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\165f1610a550fb8e44d8c430188991d8\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\165f1610a550fb8e44d8c430188991d8\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e34e43e2526342b205dac3a7764c9994\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e34e43e2526342b205dac3a7764c9994\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaefdcdcf409a8deae73ec68228806f7\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaefdcdcf409a8deae73ec68228806f7\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96cfaf8018f15a23c87a9c5ac0670037\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96cfaf8018f15a23c87a9c5ac0670037\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34ab1b24eb9acc6d6a2f073f2cf98e98\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34ab1b24eb9acc6d6a2f073f2cf98e98\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f13a88c53672959b7616efe3ca336df\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f13a88c53672959b7616efe3ca336df\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9a072a5398802c838d131e1a5397151\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9a072a5398802c838d131e1a5397151\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcdaf28699cc4114e6d32da17e246a20\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcdaf28699cc4114e6d32da17e246a20\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42497218f818ac433ceb4ed4defecc8\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e42497218f818ac433ceb4ed4defecc8\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1722e60b3e06639d9e378fb2f4330d38\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1722e60b3e06639d9e378fb2f4330d38\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d25310a652b98c95ab76086064ea410\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d25310a652b98c95ab76086064ea410\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56cb084021e871cf84ecea51eab2d876\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56cb084021e871cf84ecea51eab2d876\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83f6e05a9ce8fab4e0ab44bbe72faeae\transformed\lifecycle-viewmodel-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83f6e05a9ce8fab4e0ab44bbe72faeae\transformed\lifecycle-viewmodel-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e1debd6c2199648715ca1c7f082d854\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e1debd6c2199648715ca1c7f082d854\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52cd38d375c0bd91dd05c12938de3ad0\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52cd38d375c0bd91dd05c12938de3ad0\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8e6c90df93a834ccad23ddf3630e58\transformed\lifecycle-livedata-core-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8e6c90df93a834ccad23ddf3630e58\transformed\lifecycle-livedata-core-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220f7651e6a78cc4226b5b258cfa10a5\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220f7651e6a78cc4226b5b258cfa10a5\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25809d03e1af7273ae683fb80355c04a\transformed\lifecycle-livedata-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25809d03e1af7273ae683fb80355c04a\transformed\lifecycle-livedata-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b700a77cb751d81a7b938a4f59411e\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78b700a77cb751d81a7b938a4f59411e\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73d66cea8ba2c3602c1a4349fc81e3de\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73d66cea8ba2c3602c1a4349fc81e3de\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\990b097d5da63020171ff220dbd11022\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\990b097d5da63020171ff220dbd11022\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64781c1b5712c662595e08947f78d690\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64781c1b5712c662595e08947f78d690\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af06dbcd666720583cf708c503cacfa6\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af06dbcd666720583cf708c503cacfa6\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85c4323b1daa72591fd9057ce390656b\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85c4323b1daa72591fd9057ce390656b\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:2.3.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:2.2.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:2.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcd9dfdbda3fe2e3d4d8d8c894eeb186\transformed\transport-api-2.2.1\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:2.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcd9dfdbda3fe2e3d4d8d8c894eeb186\transformed\transport-api-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:16.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cf9cb510a63f49aa9cf84de161ee9cb\transformed\firebase-components-16.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:16.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cf9cb510a63f49aa9cf84de161ee9cb\transformed\firebase-components-16.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d717e48557e62fa5119e487bb5e82767\transformed\firebase-encoders-json-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d717e48557e62fa5119e487bb5e82767\transformed\firebase-encoders-json-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:16.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\16.1.0\267565db8531da1483692e27eb58f93ec894c78\firebase-encoders-16.1.0.jar"
      resolved="com.google.firebase:firebase-encoders:16.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b164298045610576b2a25c9cb4c23e32\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b164298045610576b2a25c9cb4c23e32\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d08a6bf331565b9a6aae0b5f313b0964\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d08a6bf331565b9a6aae0b5f313b0964\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b475231c861cbc0bde107419a5741ea5\transformed\exifinterface-1.3.2\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b475231c861cbc0bde107419a5741ea5\transformed\exifinterface-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b242992e83cfa1bb20c68975f3be6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b242992e83cfa1bb20c68975f3be6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a38ab311f18dd3ee25dbd9e1f09f96\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a38ab311f18dd3ee25dbd9e1f09f96\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.0\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="com.google.android.odml:image:1.0.0-beta1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23ab066273363ab128fd61dff960484c\transformed\image-1.0.0-beta1\jars\classes.jar"
      resolved="com.google.android.odml:image:1.0.0-beta1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23ab066273363ab128fd61dff960484c\transformed\image-1.0.0-beta1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.0.0\dbeae20d6c97b747b59ef47b6dcf770ba1a60fa6\firebase-annotations-16.0.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.0.0"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65049f970678ed445dbf29acaa5226fc\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65049f970678ed445dbf29acaa5226fc\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ab9a914a66e5faecca1870fcb3deb2a\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ab9a914a66e5faecca1870fcb3deb2a\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39a3433c3317a47f856128232e947e60\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39a3433c3317a47f856128232e947e60\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fb45d41e6a7dc95d863a72eae7b4c\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\229fb45d41e6a7dc95d863a72eae7b4c\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.3\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.3"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed8cbef73ef5c5535d65f8a6243078ba\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed8cbef73ef5c5535d65f8a6243078ba\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.6.3\b88c1bb7f149f6d2cc03898359283e57b08f39cc\auto-value-annotations-1.6.3.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.6.3"/>
</libraries>
