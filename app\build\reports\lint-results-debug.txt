D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.13.1 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.8.3 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
D:\workspace\gitee.com\wendy\face\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.8.2 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\workspace\gitee.com\wendy\face\app\src\main\java\com\wendy\face\MainActivity.kt:84: Information: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
                var imageWidth by remember { mutableStateOf(0) }
                                             ~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\java\com\wendy\face\MainActivity.kt:85: Information: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
                var imageHeight by remember { mutableStateOf(0) }
                                              ~~~~~~~~~~~~~~

   Explanation for issues of type "AutoboxingStateCreation":
   Calling mutableStateOf<T>() when T is either backed by a primitive type on
   the JVM or is a value class results in a state implementation that requires
   all state values to be boxed. This usually causes an additional allocation
   for each state write, and adds some additional work to auto-unbox values
   when reading the value of the state. Instead, prefer to use a specialized
   primitive state implementation for Int, Long, Float, and Double when the
   state does not need to track null values and does not override the default
   SnapshotMutationPolicy. See mutableIntStateOf(), mutableLongStateOf(),
   mutableFloatStateOf(), and mutableDoubleStateOf() for more information.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.runtime
   Feedback: https://issuetracker.google.com/issues/new?component=612128

D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:44: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.mlkit:face-detection:16.1.6")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:48: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.camera:camera-core:1.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:49: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.camera:camera-camera2:1.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:50: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.camera:camera-lifecycle:1.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:51: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.camera:camera-view:1.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\workspace\gitee.com\wendy\face\app\build.gradle.kts:52: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.camera:camera-extensions:1.3.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

0 errors, 24 warnings
