{"logs": [{"outputFile": "com.wendy.face.app-mergeReleaseResources-54:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4732", "endColumns": "138", "endOffsets": "4866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13744,13828", "endColumns": "83,86", "endOffsets": "13823,13910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2817,2913,3015,3116,3214,3324,3432,13383", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "2908,3010,3111,3209,3319,3427,3549,13479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6463,6578,6693,6803,6918,7016,7111,7223,7358,7474,7626,7711,7812,7904,8001,8117,8239,8345,8478,8611,8745,8909,9037,9161,9291,9411,9504,9601,9722,9845,9943,10046,10155,10296,10445,10554,10654,10738,10832,10927,11043,11130,11217,11318,11398,11484,11581,11684,11777,11874,11962,12067,12164,12263,12383,12463,12565", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "6573,6688,6798,6913,7011,7106,7218,7353,7469,7621,7706,7807,7899,7996,8112,8234,8340,8473,8606,8740,8904,9032,9156,9286,9406,9499,9596,9717,9840,9938,10041,10150,10291,10440,10549,10649,10733,10827,10922,11038,11125,11212,11313,11393,11479,11576,11679,11772,11869,11957,12062,12159,12258,12378,12458,12560,12653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3733,3839,3999,4123,4233,4389,4517,4630,4871,5040,5151,5321,5451,5614,5778,5846,5913", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "3834,3994,4118,4228,4384,4512,4625,4727,5035,5146,5316,5446,5609,5773,5841,5908,5995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,1001,1088,1160,1238,1314,1389,1467,1535", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,996,1083,1155,1233,1309,1384,1462,1530,1644"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3554,3649,6000,6095,6198,6290,6369,12658,12748,12829,12912,13082,13154,13232,13308,13484,13562,13630", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,77,75,74,77,67,113", "endOffsets": "3644,3728,6090,6193,6285,6364,6458,12743,12824,12907,12994,13149,13227,13303,13378,13557,13625,13739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\378a970638a61424dd52cc174fd66606\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,12999", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,13077"}}]}]}