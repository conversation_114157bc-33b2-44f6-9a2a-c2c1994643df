{"logs": [{"outputFile": "com.wendy.face.app-mergeReleaseResources-54:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "444,445", "startColumns": "4,4", "startOffsets": "30069,30125", "endColumns": "55,54", "endOffsets": "30120,30175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d07ad02806221aff681e526df751834b\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "276,297", "startColumns": "4,4", "startOffsets": "17822,18909", "endColumns": "41,59", "endOffsets": "17859,18964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fcdaf28699cc4114e6d32da17e246a20\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "266", "startColumns": "4", "startOffsets": "17329", "endColumns": "65", "endOffsets": "17390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50f8bd46ebe8d4f1e383c76308b17c26\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2099,2115,2121,3122,3138", "startColumns": "4,4,4,4,4", "startOffsets": "138105,138530,138708,171918,172329", "endLines": "2114,2120,2130,3137,3141", "endColumns": "24,24,24,24,24", "endOffsets": "138525,138703,138987,172324,172451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d5ee2f8e7d76ed41d62768c46df44c9a\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3046", "startColumns": "4,4,4", "startOffsets": "250,511,169748", "endLines": "7,17,3049", "endColumns": "11,11,24", "endOffsets": "397,813,169886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f9b2f3ac4e57c2a361a436e16b1cb4e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "18969", "endColumns": "53", "endOffsets": "19018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "306,355", "startColumns": "4,4", "startOffsets": "19433,23307", "endColumns": "67,166", "endOffsets": "19496,23469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\229fb45d41e6a7dc95d863a72eae7b4c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "270,273", "startColumns": "4,4", "startOffsets": "17535,17659", "endColumns": "53,66", "endOffsets": "17584,17721"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "81", "endOffsets": "133"}, "to": {"startLines": "1875", "startColumns": "4", "startOffsets": "125186", "endColumns": "80", "endOffsets": "125262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\56cb084021e871cf84ecea51eab2d876\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "18866", "endColumns": "42", "endOffsets": "18904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\27918c06b18d0382bb81e4832935fb7c\\transformed\\fragment-1.3.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "265,277,300,2774,2779", "startColumns": "4,4,4,4,4", "startOffsets": "17272,17864,19073,161409,161579", "endLines": "265,277,300,2778,2782", "endColumns": "56,64,63,24,24", "endOffsets": "17324,17924,19132,161574,161723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "307,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,1537,1547", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19501,24937,25025,25111,25192,25276,25345,25410,25493,25599,25685,25805,25859,25928,25989,26058,26147,26242,26316,26413,26506,26604,26753,26844,26932,27028,27126,27190,27258,27345,27439,27506,27578,27650,27751,27860,27936,28005,28053,28119,28183,28257,28314,28371,28443,28493,28547,28618,28689,28759,28828,28886,28962,29033,29107,29193,29243,29313,99732,100447", "endLines": "307,370,371,372,373,374,375,376,377,378,381,382,383,384,385,386,387,388,389,390,391,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,1546,1549", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19569,25020,25106,25187,25271,25340,25405,25488,25594,25680,25800,25854,25923,25984,26053,26142,26237,26311,26408,26501,26599,26748,26839,26927,27023,27121,27185,27253,27340,27434,27501,27573,27645,27746,27855,27931,28000,28048,28114,28178,28252,28309,28366,28438,28488,28542,28613,28684,28754,28823,28881,28957,29028,29102,29188,29238,29308,29373,100442,100595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\378a970638a61424dd52cc174fd66606\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,191,192,193,194,195,196,197,198,199,215,216,217,218,219,220,221,222,258,259,260,261,268,274,275,278,295,302,303,304,305,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,435,446,447,448,449,450,451,459,460,464,468,472,477,483,490,494,498,503,507,511,515,519,523,527,533,537,543,547,553,557,562,566,569,573,579,583,589,593,599,602,606,610,614,618,622,623,624,625,628,631,634,637,641,642,643,644,645,648,650,652,654,659,660,664,670,674,675,677,689,690,694,700,704,705,706,710,737,741,742,746,774,946,972,1143,1169,1200,1208,1214,1230,1252,1257,1262,1272,1281,1290,1294,1301,1320,1327,1328,1337,1340,1343,1347,1351,1355,1358,1359,1364,1369,1379,1384,1391,1397,1398,1401,1405,1410,1412,1414,1417,1420,1422,1426,1429,1436,1439,1442,1446,1448,1452,1454,1456,1458,1462,1470,1478,1490,1496,1505,1508,1519,1522,1523,1528,1529,1557,1626,1696,1697,1707,1716,1717,1719,1723,1726,1729,1732,1735,1738,1741,1744,1748,1751,1754,1757,1761,1764,1768,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1794,1796,1797,1798,1799,1800,1801,1802,1803,1805,1806,1808,1809,1811,1813,1814,1816,1817,1818,1819,1820,1821,1823,1824,1825,1826,1827,1839,1841,1843,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1859,1860,1861,1862,1863,1864,1865,1867,1871,1876,1877,1878,1879,1880,1881,1885,1886,1887,1888,1890,1892,1894,1896,1898,1899,1900,1901,1903,1905,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1921,1922,1923,1924,1926,1928,1929,1931,1932,1934,1936,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1951,1952,1953,1954,1956,1957,1958,1959,1960,1962,1964,1966,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1985,2060,2063,2066,2069,2083,2089,2131,2134,2163,2190,2199,2263,2626,2636,2674,2702,2822,2846,2852,2871,2892,3016,3036,3042,3050,3056,3110,3142,3208,3228,3283,3295,3321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,976,1040,1110,1171,1246,1322,1399,1637,1722,1804,1880,1998,2075,2153,2259,2365,2444,2524,2581,3441,3515,3590,3655,3721,3781,3842,3914,3987,4054,4122,4181,4240,4299,4358,4417,4471,4525,4578,4632,4686,4740,4926,5000,5079,5152,5226,5297,5369,5441,5655,5712,5770,5843,5917,5991,6066,6138,6211,6281,6442,6502,6605,6674,6743,6813,6887,6963,7027,7104,7180,7257,7322,7391,7468,7543,7612,7680,7757,7823,7884,7981,8046,8115,8214,8285,8344,8402,8459,8518,8582,8653,8725,8797,8869,8941,9008,9076,9144,9203,9266,9330,9420,9511,9571,9637,9704,9770,9840,9904,9957,10024,10085,10152,10265,10323,10386,10451,10516,10591,10664,10736,10780,10827,10873,10922,10983,11044,11105,11167,11231,11295,11359,11424,11487,11547,11608,11674,11733,11793,11855,11926,11986,12542,12628,12715,12805,12892,12980,13062,13145,13235,14304,14356,14414,14459,14525,14589,14646,14703,16880,16937,16985,17034,17446,17726,17773,17929,18834,19190,19254,19316,19376,19644,19718,19788,19866,19920,19990,20075,20123,20169,20230,20293,20359,20423,20494,20557,20622,20686,20747,20808,20860,20933,21007,21076,21151,21225,21299,21440,29598,30180,30258,30348,30436,30532,30622,31204,31293,31540,31821,32073,32358,32751,33228,33450,33672,33948,34175,34405,34635,34865,35095,35322,35741,35967,36392,36622,37050,37269,37552,37760,37891,38118,38544,38769,39196,39417,39842,39962,40238,40539,40863,41154,41468,41605,41736,41841,42083,42250,42454,42662,42933,43045,43157,43262,43379,43593,43739,43879,43965,44313,44401,44647,45065,45314,45396,45494,46151,46251,46503,46927,47182,47276,47365,47602,49626,49868,49970,50223,52379,63060,64576,75271,76799,78556,79182,79602,80863,82128,82384,82620,83167,83661,84266,84464,85044,86412,86787,86905,87443,87600,87796,88069,88325,88495,88636,88700,89065,89432,90108,90372,90710,91063,91157,91343,91649,91911,92036,92163,92402,92613,92732,92925,93102,93557,93738,93860,94119,94232,94419,94521,94628,94757,95032,95540,96036,96913,97207,97777,97926,98658,98830,98914,99250,99342,100906,106137,111508,111570,112148,112732,112823,112936,113165,113325,113477,113648,113814,113983,114150,114313,114556,114726,114899,115070,115344,115543,115748,116078,116162,116258,116354,116452,116552,116654,116756,116858,116960,117062,117162,117258,117370,117499,117622,117753,117884,117982,118096,118190,118330,118464,118560,118672,118772,118888,118984,119096,119196,119336,119472,119636,119766,119924,120074,120215,120359,120494,120606,120756,120884,121012,121148,121280,121410,121540,121652,122550,122696,122840,122978,123044,123134,123210,123314,123404,123506,123614,123722,123822,123902,123994,124092,124202,124254,124332,124438,124530,124634,124744,124866,125029,125267,125347,125447,125537,125647,125737,125978,126072,126178,126270,126370,126482,126596,126712,126828,126922,127036,127148,127250,127370,127492,127574,127678,127798,127924,128022,128116,128204,128316,128432,128554,128666,128841,128957,129043,129135,129247,129371,129438,129564,129632,129760,129904,130032,130101,130196,130311,130424,130523,130632,130743,130854,130955,131060,131160,131290,131381,131504,131598,131710,131796,131900,131996,132084,132202,132306,132410,132536,132624,132732,132832,132922,133032,133116,133218,133302,133356,133420,133526,133612,133722,133806,134065,136681,136799,136914,136994,137355,137588,138992,139070,140414,141775,142163,145006,155059,155397,157068,158425,162577,163328,163590,164105,164484,168762,169368,169597,169891,170106,171606,172456,175482,176226,178357,178697,180008", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,191,192,193,194,195,196,197,198,199,215,216,217,218,219,220,221,222,258,259,260,261,268,274,275,278,295,302,303,304,305,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,435,446,447,448,449,450,458,459,463,467,471,476,482,489,493,497,502,506,510,514,518,522,526,532,536,542,546,552,556,561,565,568,572,578,582,588,592,598,601,605,609,613,617,621,622,623,624,627,630,633,636,640,641,642,643,644,647,649,651,653,658,659,663,669,673,674,676,688,689,693,699,703,704,705,709,736,740,741,745,773,945,971,1142,1168,1199,1207,1213,1229,1251,1256,1261,1271,1280,1289,1293,1300,1319,1326,1327,1336,1339,1342,1346,1350,1354,1357,1358,1363,1368,1378,1383,1390,1396,1397,1400,1404,1409,1411,1413,1416,1419,1421,1425,1428,1435,1438,1441,1445,1447,1451,1453,1455,1457,1461,1469,1477,1489,1495,1504,1507,1518,1521,1522,1527,1528,1533,1625,1695,1696,1706,1715,1716,1718,1722,1725,1728,1731,1734,1737,1740,1743,1747,1750,1753,1756,1760,1763,1767,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1793,1795,1796,1797,1798,1799,1800,1801,1802,1804,1805,1807,1808,1810,1812,1813,1815,1816,1817,1818,1819,1820,1822,1823,1824,1825,1826,1827,1840,1842,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1858,1859,1860,1861,1862,1863,1864,1866,1870,1874,1876,1877,1878,1879,1880,1884,1885,1886,1887,1889,1891,1893,1895,1897,1898,1899,1900,1902,1904,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1920,1921,1922,1923,1925,1927,1928,1930,1931,1933,1935,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1950,1951,1952,1953,1955,1956,1957,1958,1959,1961,1963,1965,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,2059,2062,2065,2068,2082,2088,2098,2133,2162,2189,2198,2262,2625,2629,2663,2701,2719,2845,2851,2857,2891,3015,3035,3041,3045,3055,3090,3121,3207,3227,3282,3294,3320,3327", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,971,1035,1105,1166,1241,1317,1394,1472,1717,1799,1875,1951,2070,2148,2254,2360,2439,2519,2576,2634,3510,3585,3650,3716,3776,3837,3909,3982,4049,4117,4176,4235,4294,4353,4412,4466,4520,4573,4627,4681,4735,4789,4995,5074,5147,5221,5292,5364,5436,5509,5707,5765,5838,5912,5986,6061,6133,6206,6276,6347,6497,6558,6669,6738,6808,6882,6958,7022,7099,7175,7252,7317,7386,7463,7538,7607,7675,7752,7818,7879,7976,8041,8110,8209,8280,8339,8397,8454,8513,8577,8648,8720,8792,8864,8936,9003,9071,9139,9198,9261,9325,9415,9506,9566,9632,9699,9765,9835,9899,9952,10019,10080,10147,10260,10318,10381,10446,10511,10586,10659,10731,10775,10822,10868,10917,10978,11039,11100,11162,11226,11290,11354,11419,11482,11542,11603,11669,11728,11788,11850,11921,11981,12049,12623,12710,12800,12887,12975,13057,13140,13230,13321,14351,14409,14454,14520,14584,14641,14698,14752,16932,16980,17029,17080,17475,17768,17817,17970,18861,19249,19311,19371,19428,19713,19783,19861,19915,19985,20070,20118,20164,20225,20288,20354,20418,20489,20552,20617,20681,20742,20803,20855,20928,21002,21071,21146,21220,21294,21435,21505,29646,30253,30343,30431,30527,30617,31199,31288,31535,31816,32068,32353,32746,33223,33445,33667,33943,34170,34400,34630,34860,35090,35317,35736,35962,36387,36617,37045,37264,37547,37755,37886,38113,38539,38764,39191,39412,39837,39957,40233,40534,40858,41149,41463,41600,41731,41836,42078,42245,42449,42657,42928,43040,43152,43257,43374,43588,43734,43874,43960,44308,44396,44642,45060,45309,45391,45489,46146,46246,46498,46922,47177,47271,47360,47597,49621,49863,49965,50218,52374,63055,64571,75266,76794,78551,79177,79597,80858,82123,82379,82615,83162,83656,84261,84459,85039,86407,86782,86900,87438,87595,87791,88064,88320,88490,88631,88695,89060,89427,90103,90367,90705,91058,91152,91338,91644,91906,92031,92158,92397,92608,92727,92920,93097,93552,93733,93855,94114,94227,94414,94516,94623,94752,95027,95535,96031,96908,97202,97772,97921,98653,98825,98909,99245,99337,99615,106132,111503,111565,112143,112727,112818,112931,113160,113320,113472,113643,113809,113978,114145,114308,114551,114721,114894,115065,115339,115538,115743,116073,116157,116253,116349,116447,116547,116649,116751,116853,116955,117057,117157,117253,117365,117494,117617,117748,117879,117977,118091,118185,118325,118459,118555,118667,118767,118883,118979,119091,119191,119331,119467,119631,119761,119919,120069,120210,120354,120489,120601,120751,120879,121007,121143,121275,121405,121535,121647,121787,122691,122835,122973,123039,123129,123205,123309,123399,123501,123609,123717,123817,123897,123989,124087,124197,124249,124327,124433,124525,124629,124739,124861,125024,125181,125342,125442,125532,125642,125732,125973,126067,126173,126265,126365,126477,126591,126707,126823,126917,127031,127143,127245,127365,127487,127569,127673,127793,127919,128017,128111,128199,128311,128427,128549,128661,128836,128952,129038,129130,129242,129366,129433,129559,129627,129755,129899,130027,130096,130191,130306,130419,130518,130627,130738,130849,130950,131055,131155,131285,131376,131499,131593,131705,131791,131895,131991,132079,132197,132301,132405,132531,132619,132727,132827,132917,133027,133111,133213,133297,133351,133415,133521,133607,133717,133801,133921,136676,136794,136909,136989,137350,137583,138100,139065,140409,141770,142158,145001,155054,155189,156762,158420,158992,163323,163585,163785,164479,168757,169363,169592,169743,170101,171184,171913,175477,176221,178352,178692,180003,180206"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "337", "startColumns": "4", "startOffsets": "21593", "endColumns": "43", "endOffsets": "21632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,43,44,75,76,184,185,186,187,188,189,190,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225,271,272,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,308,338,339,340,341,342,343,344,440,1828,1829,1833,1834,1838,1983,1984,2630,2664,2720,2753,2783,2816", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1477,1549,2639,2704,4794,4863,12054,12124,12192,12264,12334,12395,12469,13326,13387,13448,13510,13574,13636,13697,13765,13865,13925,13991,14064,14133,14190,14242,14757,14829,14905,17589,17624,17975,18030,18093,18148,18206,18264,18325,18388,18445,18496,18546,18607,18664,18730,18764,18799,19574,21637,21704,21776,21845,21914,21988,22060,29825,121792,121909,122110,122220,122421,133926,133998,155194,156767,158997,160728,161728,162410", "endLines": "9,28,29,43,44,75,76,184,185,186,187,188,189,190,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,223,224,225,271,272,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,308,338,339,340,341,342,343,344,440,1828,1832,1833,1837,1838,1983,1984,2635,2673,2752,2773,2815,2821", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1544,1632,2699,2765,4858,4921,12119,12187,12259,12329,12390,12464,12537,13382,13443,13505,13569,13631,13692,13760,13860,13920,13986,14059,14128,14185,14237,14299,14824,14900,14965,17619,17654,18025,18088,18143,18201,18259,18320,18383,18440,18491,18541,18602,18659,18725,18759,18794,18829,19639,21699,21771,21840,21909,21983,22055,22143,29891,121904,122105,122215,122416,122545,133993,134060,155392,157063,160723,161404,162405,162572"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,85,86,87,98,99,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1956,5514,5561,5608,6352,6397,6563", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1993,5556,5603,5650,6392,6437,6600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "45,46,47,48,49,50,51,52,347,348,349,350,351,352,353,354,356,357,358,359,360,361,362,363,364,2858,3091", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2770,2860,2940,3030,3120,3200,3281,3361,22267,22372,22553,22678,22785,22965,23088,23204,23474,23662,23767,23948,24073,24248,24396,24459,24521,163790,171189", "endLines": "45,46,47,48,49,50,51,52,347,348,349,350,351,352,353,354,356,357,358,359,360,361,362,363,364,2870,3109", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2855,2935,3025,3115,3195,3276,3356,3436,22367,22548,22673,22780,22960,23083,23199,23302,23657,23762,23943,24068,24243,24391,24454,24516,24595,164100,171601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,262,263,264,267,269,301,345,346,365,366,367,368,369,431,432,433,434,436,437,438,439,441,442,443,1534,1550,1553", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14970,15029,15088,15148,15208,15268,15328,15388,15448,15508,15568,15628,15688,15747,15807,15867,15927,15987,16047,16107,16167,16227,16287,16347,16406,16466,16526,16585,16644,16703,16762,16821,17085,17159,17217,17395,17480,19137,22148,22213,24600,24666,24767,24825,24877,29378,29440,29494,29544,29651,29697,29743,29785,29896,29943,29979,99620,100600,100711", "endLines": "226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,262,263,264,267,269,301,345,346,365,366,367,368,369,431,432,433,434,436,437,438,439,441,442,443,1536,1552,1556", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "15024,15083,15143,15203,15263,15323,15383,15443,15503,15563,15623,15683,15742,15802,15862,15922,15982,16042,16102,16162,16222,16282,16342,16401,16461,16521,16580,16639,16698,16757,16816,16875,17154,17212,17267,17441,17530,19185,22208,22262,24661,24762,24820,24872,24932,29435,29489,29539,29593,29692,29738,29780,29820,29938,29974,30064,99727,100706,100901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e1debd6c2199648715ca1c7f082d854\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "299", "startColumns": "4", "startOffsets": "19023", "endColumns": "49", "endOffsets": "19068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d4c5419693f837eebddb887747f95a1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "336", "startColumns": "4", "startOffsets": "21510", "endColumns": "82", "endOffsets": "21588"}}]}]}