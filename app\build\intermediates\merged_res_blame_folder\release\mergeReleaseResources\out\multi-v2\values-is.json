{"logs": [{"outputFile": "com.wendy.face.app-mergeReleaseResources-54:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2779,2874,2981,3078,3178,3281,3385,13170", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "2869,2976,3073,3173,3276,3380,3491,13266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,984,1067,1137,1212,1287,1361,1438,1506", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,979,1062,1132,1207,1282,1356,1433,1501,1621"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3496,3587,5816,5915,6014,6099,6179,12463,12552,12634,12712,12876,12946,13021,13096,13271,13348,13416", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "3582,3663,5910,6009,6094,6174,6269,12547,12629,12707,12790,12941,13016,13091,13165,13343,13411,13531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4642", "endColumns": "128", "endOffsets": "4766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6274,6388,6500,6607,6719,6816,6915,7031,7172,7299,7434,7524,7625,7722,7822,7937,8063,8169,8294,8418,8560,8731,8854,8970,9089,9211,9309,9407,9516,9638,9744,9852,9955,10085,10220,10328,10433,10509,10603,10696,10810,10895,10980,11089,11169,11260,11361,11462,11557,11665,11753,11858,11959,12065,12185,12265,12367", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "6383,6495,6602,6714,6811,6910,7026,7167,7294,7429,7519,7620,7717,7817,7932,8058,8164,8289,8413,8555,8726,8849,8965,9084,9206,9304,9402,9511,9633,9739,9847,9950,10080,10215,10323,10428,10504,10598,10691,10805,10890,10975,11084,11164,11255,11356,11457,11552,11660,11748,11853,11954,12060,12180,12260,12362,12458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13536,13623", "endColumns": "86,86", "endOffsets": "13618,13705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\378a970638a61424dd52cc174fd66606\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,12795", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,12871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3668,3774,3928,4053,4162,4303,4428,4537,4771,4925,5031,5188,5314,5456,5610,5674,5737", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "3769,3923,4048,4157,4298,4423,4532,4637,4920,5026,5183,5309,5451,5605,5669,5732,5811"}}]}]}