1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wendy.face"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera.any" />
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:5-64
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:19-61
12
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission
14-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:5-8:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:8:9-35
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:5-80
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:22-77
18
19    <queries>
19-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
20        <intent>
20-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
21            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30    <!-- <uses-sdk android:minSdkVersion="14"/> -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
32    <uses-permission android:name="android.permission.INTERNET" />
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
33
34    <application
34-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:11:5-32:19
35        android:allowBackup="true"
35-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:12:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:13:9-65
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:14:9-54
40        android:icon="@mipmap/ic_launcher"
40-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:15:9-43
41        android:label="@string/app_name"
41-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:16:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:17:9-54
43        android:supportsRtl="true"
43-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:18:9-35
44        android:theme="@style/Theme.Face" >
44-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:19:9-42
45        <activity
45-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:21:9-31:20
46            android:name="com.wendy.face.MainActivity"
46-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:22:13-41
47            android:exported="true"
47-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:23:13-36
48            android:label="@string/app_name"
48-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24:13-45
49            android:theme="@style/Theme.Face" >
49-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:25:13-46
50            <intent-filter>
50-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:26:13-30:29
51                <action android:name="android.intent.action.MAIN" />
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:17-69
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:17-77
53-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:27-74
54            </intent-filter>
55        </activity>
56
57        <uses-library
57-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
58            android:name="androidx.camera.extensions.impl"
58-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
59            android:required="false" />
59-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
60
61        <service
61-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
62            android:name="androidx.camera.core.impl.MetadataHolderService"
62-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
63            android:enabled="false"
63-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
64            android:exported="false" >
64-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
66                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
66-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
67                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
67-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
68        </service>
69        <service
69-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
70            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
70-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
71            android:directBootAware="true"
71-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:17:13-43
72            android:exported="false" >
72-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
73            <meta-data
73-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
74                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
74-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
75                android:value="com.google.firebase.components.ComponentRegistrar" />
75-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef56dd88d86ed3ca6ef1bad58016681\transformed\play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
76            <meta-data
76-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:12:13-14:85
77                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
77-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:13:17-124
78                android:value="com.google.firebase.components.ComponentRegistrar" />
78-->[com.google.mlkit:vision-common:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f45b269b8d1a1996fea8c340ec467abf\transformed\vision-common-17.2.0\AndroidManifest.xml:14:17-82
79            <meta-data
79-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:20:13-22:85
80                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
80-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:21:17-120
81                android:value="com.google.firebase.components.ComponentRegistrar" />
81-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:22:17-82
82        </service>
83
84        <provider
84-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:9:9-13:38
85            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
85-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:10:13-78
86            android:authorities="com.wendy.face.mlkitinitprovider"
86-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:11:13-69
87            android:exported="false"
87-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:12:13-37
88            android:initOrder="99" />
88-->[com.google.mlkit:common:18.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b0419a5612395d350313afcebc2aeaf\transformed\common-18.10.0\AndroidManifest.xml:13:13-35
89
90        <activity
90-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
91            android:name="com.google.android.gms.common.api.GoogleApiActivity"
91-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
92            android:exported="false"
92-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
93            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
93-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
94
95        <meta-data
95-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
96            android:name="com.google.android.gms.version"
96-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
97            android:value="@integer/google_play_services_version" />
97-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
98
99        <provider
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
100            android:name="androidx.startup.InitializationProvider"
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
101            android:authorities="com.wendy.face.androidx-startup"
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
102            android:exported="false" >
102-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
103            <meta-data
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.emoji2.text.EmojiCompatInitializer"
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
105                android:value="androidx.startup" />
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
107-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
108                android:value="androidx.startup" />
108-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
109            <meta-data
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
111                android:value="androidx.startup" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
112        </provider>
113
114        <service
114-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
115            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
115-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
116            android:exported="false" >
116-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
117            <meta-data
117-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
118                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
118-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
119                android:value="cct" />
119-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
120        </service>
121        <service
121-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
122            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
122-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
123            android:exported="false"
123-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
124            android:permission="android.permission.BIND_JOB_SERVICE" >
124-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
125        </service>
126
127        <receiver
127-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
128            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
128-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
129            android:exported="false" />
129-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
130        <receiver
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
131            android:name="androidx.profileinstaller.ProfileInstallReceiver"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
132            android:directBootAware="false"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
133            android:enabled="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
134            android:exported="true"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
135            android:permission="android.permission.DUMP" >
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
137                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
140                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
143                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
146                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
147            </intent-filter>
148        </receiver>
149    </application>
150
151</manifest>
