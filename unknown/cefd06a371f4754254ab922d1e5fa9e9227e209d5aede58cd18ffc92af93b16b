package com.wendy.face.ui.components

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * 相机控制面板组件
 * 包含拍照、切换摄像头、测试等功能按钮
 */
@Composable
fun CameraControls(
    onCameraSwitch: () -> Unit,
    onCapture: () -> Unit,
    onTest: () -> Unit
) {
    // 新氧医美风格控制面板
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .shadow(12.dp, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)),
        shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Camera switch button - 小圆按钮
            FloatingActionButton(
                onClick = {
                    Log.d("CameraControls", "Camera switch button clicked")
                    onCameraSwitch()
                },
                modifier = Modifier.size(56.dp),
                containerColor = Color.White.copy(alpha = 0.1f),
                contentColor = Color.White
            ) {
                Text(
                    text = "🔄",
                    style = MaterialTheme.typography.headlineSmall
                )
            }

            // Capture button - 大圆按钮（新氧风格）
            FloatingActionButton(
                onClick = {
                    Log.d("CameraControls", "Capture button clicked")
                    onCapture()
                },
                modifier = Modifier
                    .size(80.dp)
                    .shadow(8.dp, CircleShape),
                containerColor = Color.Transparent
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color.Cyan,
                                    Color.Blue,
                                    Color.Magenta
                                )
                            ),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "📸",
                        style = MaterialTheme.typography.headlineLarge.copy(
                            color = Color.White
                        )
                    )
                }
            }

            // Test button - 小圆按钮
            FloatingActionButton(
                onClick = {
                    Log.d("CameraControls", "Test button clicked")
                    onTest()
                },
                modifier = Modifier.size(56.dp),
                containerColor = Color.White.copy(alpha = 0.1f),
                contentColor = Color.White
            ) {
                Text(
                    text = "🧪",
                    style = MaterialTheme.typography.headlineSmall
                )
            }
        }
    }
}
